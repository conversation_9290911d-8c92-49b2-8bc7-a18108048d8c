import React, { useState } from 'react';
import clsx from 'clsx';
import Link from '@docusaurus/Link';
import Layout from '@theme/Layout';
import Translate, { translate } from '@docusaurus/Translate';
import Heading from '@theme/Heading';

import styles from './index.module.css';
import mindmapStyles from './ai-mindmap.module.css';
import Footer from '../components/Footer';
import FAQSection from '../components/FAQSection';
import TestimonialsSection from '../components/TestimonialsSection';
import ImageModal from '../components/ImageModal';
import GoogleAccountAnalytics from '../components/GoogleAccountAnalytics';
import CTASection from '../components/CTASection';
import MindmapStructuredData from '../components/MindmapStructuredData';
import ComparisonSection from '../components/ComparisonSection';
import VideoSection from '../components/VideoSection';
import BenefitsSection from '../components/BenefitsSection';
import SocialProofSection from '../components/SocialProofSection';

function MindmapHeader({ setShowImageSrc }) {
  return (
    <section id="hero" className={clsx(mindmapStyles.hero, mindmapStyles.pageSection)} style={{
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    }}>
      <div className="container">
        <div className={mindmapStyles.heroRow}>
          <div className={mindmapStyles.heroContent} style={{ flex: 1, minWidth: 0 }}>
            <div className={mindmapStyles.heroBadge}>
              <Translate id="ai_mindmap.hero.badge">
                NEW CHROME EXTENSION
              </Translate>
            </div>
            <Heading as="h1">
              <Translate id="ai_mindmap.hero.title">
                Transform Any Web Content into Mind Maps with AI
              </Translate>
            </Heading>
            <p className={mindmapStyles.heroSubtitle}>
              <Translate id="ai_mindmap.hero.subtitle">
                One-click mind mapping from web pages, YouTube videos, and AI conversations. Your gateway to FunBlocks AIFlow.
              </Translate>
            </p>

            <div className={mindmapStyles.heroButtons}>
              <Link
                className={clsx('button', mindmapStyles.btnPrimary)}
                to="https://chromewebstore.google.com/detail/ai-mindmap-mind-mapping-g/nlalnbdblcdgnammbelmmngehcloildo"
              >
                <Translate id="ai_mindmap.hero.download">Download Extension for FREE</Translate>
              </Link>
              <Link
                className={clsx('button', mindmapStyles.btnSecondary)}
                to="#how-it-works"
              >
                <Translate id="ai_mindmap.hero.learn_more">See How It Works</Translate>
              </Link>
            </div>

            <div className={mindmapStyles.heroStats}>
              <div className={mindmapStyles.heroStat}>
                <span className={mindmapStyles.heroStatNumber}>30+</span>
                <span className={mindmapStyles.heroStatLabel}>
                  <Translate id="ai_mindmap.hero.stat1">Free Mind Maps</Translate>
                </span>
              </div>
              <div className={mindmapStyles.heroStat}>
                <span className={mindmapStyles.heroStatNumber}>10</span>
                <span className={mindmapStyles.heroStatLabel}>
                  <Translate id="ai_mindmap.hero.stat2">Daily Free Uses</Translate>
                </span>
              </div>
              <div className={mindmapStyles.heroStat}>
                <span className={mindmapStyles.heroStatNumber}>4.8★</span>
                <span className={mindmapStyles.heroStatLabel}>
                  <Translate id="ai_mindmap.hero.stat3">User Rating</Translate>
                </span>
              </div>
            </div>
          </div>
          <div className={mindmapStyles.heroImageContainer} style={{ flex: 1, minWidth: 0, display: 'flex', justifyContent: 'center' }}>
            <div className={mindmapStyles.heroImageWrapper}>
              <img
                className={mindmapStyles.heroImage}
                onClick={() => setShowImageSrc("/img/portfolio/fullsize/ai_mindmap_sidebar.png")}
                id="ai-mindmap-overview"
                alt="FunBlocks AI Mindmap Extension interface"
                src="/img/portfolio/fullsize/ai_mindmap_sidebar.png"
              />
              {/* <div className={mindmapStyles.heroImageOverlay}>
                <span className={mindmapStyles.heroImageOverlayText}>
                  <Translate id="ai_mindmap.hero.image_caption">Click to enlarge</Translate>
                </span>
              </div> */}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

function MindmapShowcase({ setShowImageSrc }) {
  return (
    <section id="hero" className={clsx(mindmapStyles.hero, mindmapStyles.pageSection)} style={{
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      width: 1280,
      height: 800,
      paddingTop: 100,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'column'
    }}>
      <div className="container">
          <div className={mindmapStyles.heroContent} >
            <div className={mindmapStyles.heroBadge}>
              <Translate id="ai_mindmap.hero.badge">
                NEW CHROME EXTENSION
              </Translate>
            </div>
            <Heading as="h1">
              AI Prompt Optimizer & Response Analyzer
            </Heading>
            <p className={mindmapStyles.heroSubtitle}>
              Enhance your AI conversations with prompt optimization and critical thinking tools.
            </p>
            <p className={mindmapStyles.heroSubtitle}>
              Let AI help you think better, not replace your thinking.
            </p>


          </div>
          <div className={mindmapStyles.heroImageWrapper}>
            <img
              className={mindmapStyles.heroImage}
              onClick={() => setShowImageSrc("/img/portfolio/fullsize/prompt_optimizer_hero.png")}
              id="ai-mindmap-overview"
              alt="FunBlocks AI Mindmap Extension interface"
              src="/img/portfolio/fullsize/prompt_optimizer_hero.png"
            />

          </div>
        </div>
    </section>
  );
}

// function MindmapShowcase2({ setShowImageSrc }) {
//   return (
//     <section id="hero" className={clsx(mindmapStyles.hero, mindmapStyles.pageSection)} style={{
//       background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
//       width: 1280,
//       height: 800,
//       display: 'flex',
//       alignItems: 'center',
//       justifyContent: 'center'
//     }}>
//       <div className="container">
//         <div className={mindmapStyles.heroRow}>
//            <div className={mindmapStyles.heroImageWrapper} style={{flex: 3}}>
//               <img
//                 className={mindmapStyles.heroImage}
//                 onClick={() => setShowImageSrc("/img/portfolio/fullsize/ai_mindmap_youtube.png")}
//                 id="ai-mindmap-overview"
//                 alt="FunBlocks AI Mindmap Extension interface"
//                 src="/img/portfolio/fullsize/ai_mindmap_youtube.png"
//               />

//             </div>
//           <div className={mindmapStyles.heroContent} style={{ flex: 2, minWidth: 0 }}>
//             <div className={mindmapStyles.heroBadge}>
//                 YOUTUBE VIDEOS
//             </div>
//             <Heading as="h1">
//                 YouTube Video Mind Maps
//             </Heading>
//             <p className={mindmapStyles.heroSubtitle}>
//                 Perfect for educational videos, lectures, and tutorials.
//             </p>


//           </div>
//         </div>
//       </div>
//     </section>
//   );
// }

// function MindmapShowcase3({ setShowImageSrc }) {
//   return (
//     <section id="hero" className={clsx(mindmapStyles.hero, mindmapStyles.pageSection)} style={{
//       background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
//       width: 1280,
//       height: 800,
//       display: 'flex',
//       alignItems: 'center',
//       justifyContent: 'center'
//     }}>
//       <div className="container">
//         <div className={mindmapStyles.heroRow}>

//           <div className={mindmapStyles.heroContent} style={{ flex: 2, minWidth: 0 }}>
//             <div className={mindmapStyles.heroBadge}>
//               AI BRAINSTORMING
//             </div>
//             <Heading as="h1">
//               AI-Powered Brainstorming & Analysis
//             </Heading>
//             <p className={mindmapStyles.heroSubtitle}>
//               Generate mind maps from any topic, perform critical analysis, and explore complex subjects with AI assistance.
//             </p>
//           </div>

//           <div className={mindmapStyles.heroImageWrapper} style={{ flex: 3 }}>
//             <img
//               className={mindmapStyles.heroImage}
//               onClick={() => setShowImageSrc("/img/portfolio/fullsize/ai_mindmap_youtube.png")}
//               id="ai-mindmap-overview"
//               alt="FunBlocks AI Mindmap Extension interface"
//               src="/img/portfolio/fullsize/ai_mindmap_brainstorming.png"
//             />
//           </div>
//         </div>
//       </div>
//     </section>
//   );
// }

function MindmapShowcase3({ setShowImageSrc }) {
  return (
    <section id="hero" className={clsx(mindmapStyles.hero, mindmapStyles.pageSection)} style={{
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      width: 1280,
      height: 800,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      <div className="container">
        <div className={mindmapStyles.heroRow}>
          <div className={mindmapStyles.heroImageWrapper} style={{ flex: 3 }}>
            <img
              className={mindmapStyles.heroImage}
              onClick={() => setShowImageSrc("/img/portfolio/fullsize/ai_mindmap_youtube.png")}
              id="ai-mindmap-overview"
              alt="FunBlocks AI Mindmap Extension interface"
              src="/img/portfolio/fullsize/aiflow_benefits.png"
            />
          </div>

          <div className={mindmapStyles.heroContent} style={{ flex: 2, minWidth: 0 }}>
            <div className={mindmapStyles.heroBadge}>
              DEEP EXPLORATION
            </div>
            <Heading as="h1">
              Seamless Integration with FunBlocks AIFlow
            </Heading>
            <p className={mindmapStyles.heroSubtitle}>
              One-click save to FunBlocks AIFlow workspace. Continue exploration with advanced thinking tools.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}

function HowItWorksSection({ setShowImageSrc }) {
  return (
    <section id="how-it-works" className={mindmapStyles.featureSection}>
      <div className="container">
        <div className={mindmapStyles.sectionHeading}>
          <Heading as="h2" className={mindmapStyles.sectionTitle}>
            <Translate id="ai_mindmap.how_it_works.title">How AI Mindmap Works</Translate>
          </Heading>
          <p className={mindmapStyles.sectionDescription}>
            <Translate id="ai_mindmap.how_it_works.description">
              Transform any web content into visual mind maps with just one click. Perfect for learning, research, and creative thinking.
            </Translate>
          </p>
        </div>

        <div className={mindmapStyles.workflowSteps}>
          <div className={mindmapStyles.workflowStep}>
            <div className={mindmapStyles.stepNumber}>1</div>
            <Heading as="h3" className={mindmapStyles.stepTitle}>
              <Translate id="ai_mindmap.how_it_works.step1.title">Install the Extension</Translate>
            </Heading>
            <p className={mindmapStyles.stepDescription}>
              <Translate id="ai_mindmap.how_it_works.step1.description">
                Add AI Mindmap to Chrome in seconds. No complex setup required.
              </Translate>
            </p>
          </div>
          <div className={mindmapStyles.workflowStep}>
            <div className={mindmapStyles.stepNumber}>2</div>
            <Heading as="h3" className={mindmapStyles.stepTitle}>
              <Translate id="ai_mindmap.how_it_works.step2.title">Browse Any Website</Translate>
            </Heading>
            <p className={mindmapStyles.stepDescription}>
              <Translate id="ai_mindmap.how_it_works.step2.description">
                Visit any webpage or YouTube video and click the AI Mindmap icon to extract content.
              </Translate>
            </p>
          </div>
          <div className={mindmapStyles.workflowStep}>
            <div className={mindmapStyles.stepNumber}>3</div>
            <Heading as="h3" className={mindmapStyles.stepTitle}>
              <Translate id="ai_mindmap.how_it_works.step3.title">Generate Mind Maps</Translate>
            </Heading>
            <p className={mindmapStyles.stepDescription}>
              <Translate id="ai_mindmap.how_it_works.step3.description">
                Watch as AI transforms complex content into clear, visual mind maps instantly.
              </Translate>
            </p>
          </div>
        </div>

        {/* Feature Grid for Web Content */}
        <div className={mindmapStyles.featureGrid}>
          <div className={mindmapStyles.featureContent}>
            <div className={mindmapStyles.featureBadge}>
              <Translate id="ai_mindmap.how_it_works.feature1.badge">WEB CONTENT</Translate>
            </div>
            <Heading as="h3" className={mindmapStyles.featureTitle}>
              <Translate id="ai_mindmap.how_it_works.feature1.title">One-Click Web Page Mind Mapping</Translate>
            </Heading>
            <p className={mindmapStyles.featureDescription}>
              <Translate id="ai_mindmap.how_it_works.feature1.description">
                Transform any article, blog post, or web page into a structured mind map. Perfect for research, learning, and content analysis.
              </Translate>
            </p>
            <ul className={mindmapStyles.featureList}>
              <li>
                <div className={mindmapStyles.featureIcon}>📄</div>
                <div>
                  <Translate id="ai_mindmap.how_it_works.feature1.point1">
                    Automatically extract key concepts and main ideas from web pages
                  </Translate>
                </div>
              </li>
              <li>
                <div className={mindmapStyles.featureIcon}>🎯</div>
                <div>
                  <Translate id="ai_mindmap.how_it_works.feature1.point2">
                    Organize information hierarchically for better understanding
                  </Translate>
                </div>
              </li>
              <li>
                <div className={mindmapStyles.featureIcon}>💾</div>
                <div>
                  <Translate id="ai_mindmap.how_it_works.feature1.point3">
                    Save to FunBlocks AIFlow for further exploration and development
                  </Translate>
                </div>
              </li>
            </ul>
          </div>
          <div className={mindmapStyles.featureImageWrapper}>
            <div className={mindmapStyles.featureImageContainer} onClick={() => setShowImageSrc("/img/portfolio/fullsize/ai_mindmap_webpage.png")}>
              <img
                className={mindmapStyles.featureImage}
                id="ai-mindmap-webpage"
                alt="AI Mindmap web page content extraction"
                src="/img/portfolio/fullsize/ai_mindmap_sidebar.png"
              />
              {/* <div className={mindmapStyles.featureImageOverlay}>
                <span className={mindmapStyles.featureImageOverlayText}>
                  <Translate id="ai_mindmap.how_it_works.feature1.image_caption">Click to enlarge</Translate>
                </span>
              </div> */}
            </div>
          </div>
        </div>

        {/* Feature Grid for YouTube */}
        <div className={mindmapStyles.featureGrid} style={{ flexDirection: 'row-reverse' }}>
          <div className={mindmapStyles.featureContent}>
            <div className={mindmapStyles.featureBadge} style={{ backgroundColor: '#ff4444', color: 'white' }}>
              <Translate id="ai_mindmap.how_it_works.feature2.badge">YOUTUBE VIDEOS</Translate>
            </div>
            <Heading as="h3" className={mindmapStyles.featureTitle}>
              <Translate id="ai_mindmap.how_it_works.feature2.title">YouTube Video Mind Maps</Translate>
            </Heading>
            <p className={mindmapStyles.featureDescription}>
              <Translate id="ai_mindmap.how_it_works.feature2.description">
                Convert YouTube video transcripts into visual mind maps. Perfect for educational videos, lectures, and tutorials.
              </Translate>
            </p>
            <ul className={mindmapStyles.featureList}>
              <li>
                <div className={mindmapStyles.featureIcon}>🎥</div>
                <div>
                  <Translate id="ai_mindmap.how_it_works.feature2.point1">
                    Extract key points from video transcripts automatically
                  </Translate>
                </div>
              </li>
              <li>
                <div className={mindmapStyles.featureIcon}>📚</div>
                <div>
                  <Translate id="ai_mindmap.how_it_works.feature2.point2">
                    Perfect for educational content and online learning
                  </Translate>
                </div>
              </li>
              <li>
                <div className={mindmapStyles.featureIcon}>⏰</div>
                <div>
                  <Translate id="ai_mindmap.how_it_works.feature2.point3">
                    Save hours of note-taking with instant visual summaries
                  </Translate>
                </div>
              </li>
            </ul>
          </div>
          <div className={mindmapStyles.featureImageWrapper}>
            <div className={mindmapStyles.featureImageContainer} onClick={() => setShowImageSrc("/img/portfolio/fullsize/ai_mindmap_youtube.png")}>
              <img
                className={mindmapStyles.featureImage}
                id="ai-mindmap-youtube"
                alt="AI Mindmap YouTube video transcript analysis"
                src="/img/portfolio/fullsize/ai_mindmap_youtube.png"
              />
              {/* <div className={mindmapStyles.featureImageOverlay}>
                <span className={mindmapStyles.featureImageOverlayText}>
                  <Translate id="ai_mindmap.how_it_works.feature2.image_caption">Click to enlarge</Translate>
                </span>
              </div> */}
            </div>
          </div>
        </div>

        {/* Feature Grid for AI Brainstorming */}
        <div className={mindmapStyles.featureGrid}>
          <div className={mindmapStyles.featureContent}>
            <div className={mindmapStyles.featureBadge} style={{ backgroundColor: '#9c27b0', color: 'white' }}>
              <Translate id="ai_mindmap.how_it_works.feature3.badge">AI BRAINSTORMING</Translate>
            </div>
            <Heading as="h3" className={mindmapStyles.featureTitle}>
              <Translate id="ai_mindmap.how_it_works.feature3.title">AI-Powered Brainstorming & Analysis</Translate>
            </Heading>
            <p className={mindmapStyles.featureDescription}>
              <Translate id="ai_mindmap.how_it_works.feature3.description">
                Generate mind maps from any topic, perform critical analysis, and explore complex subjects with AI assistance.
              </Translate>
            </p>
            <ul className={mindmapStyles.featureList}>
              <li>
                <div className={mindmapStyles.featureIcon}>💡</div>
                <div>
                  <Translate id="ai_mindmap.how_it_works.feature3.point1">
                    AI brainstorming with classic thinking models
                  </Translate>
                </div>
              </li>
              <li>
                <div className={mindmapStyles.featureIcon}>🔍</div>
                <div>
                  <Translate id="ai_mindmap.how_it_works.feature3.point2">
                    Critical analysis and decision-making support
                  </Translate>
                </div>
              </li>
              <li>
                <div className={mindmapStyles.featureIcon}>🧩</div>
                <div>
                  <Translate id="ai_mindmap.how_it_works.feature3.point3">
                    Break down complex topics for better understanding
                  </Translate>
                </div>
              </li>
            </ul>
          </div>
          <div className={mindmapStyles.featureImageWrapper}>
            <div className={mindmapStyles.featureImageContainer} onClick={() => setShowImageSrc("/img/portfolio/fullsize/ai_mindmap_brainstorming.png")}>
              <img
                className={mindmapStyles.featureImage}
                id="ai-mindmap-brainstorming"
                alt="AI Mindmap brainstorming and critical thinking"
                src="/img/portfolio/fullsize/ai_mindmap_brainstorming.png"
              />
              {/* <div className={mindmapStyles.featureImageOverlay}>
                <span className={mindmapStyles.featureImageOverlayText}>
                  <Translate id="ai_mindmap.how_it_works.feature3.image_caption">Click to enlarge</Translate>
                </span>
              </div> */}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

function AIFlowIntegrationSection({ setShowImageSrc }) {
  return (
    <section id="aiflow-integration" className={mindmapStyles.featureSection} style={{ backgroundColor: '#f0f8ff' }}>
      <div className="container">
        <div className={mindmapStyles.sectionHeading}>
          <Heading as="h2" className={mindmapStyles.sectionTitle}>
            <Translate id="ai_mindmap.aiflow_integration.title">Seamless Integration with FunBlocks AIFlow</Translate>
          </Heading>
          <p className={mindmapStyles.sectionDescription}>
            <Translate id="ai_mindmap.aiflow_integration.description">
              AI Mindmap serves as your quick entry point to the powerful FunBlocks AIFlow ecosystem. Start anywhere, explore everywhere.
            </Translate>
          </p>
        </div>

        <div className={mindmapStyles.featureGrid}>
          <div className={mindmapStyles.featureContent}>
            <Heading as="h3" className={mindmapStyles.featureTitle}>
              <Translate id="ai_mindmap.aiflow_integration.gateway.title">Your Gateway to AIFlow</Translate>
            </Heading>
            <p className={mindmapStyles.featureDescription}>
              <Translate id="ai_mindmap.aiflow_integration.gateway.description">
                Transform web browsing into active learning. Every mind map you create can be saved and expanded in FunBlocks AIFlow for deeper exploration.
              </Translate>
            </p>
            <ul className={mindmapStyles.featureList}>
              <li>
                <div className={mindmapStyles.featureIcon}>🚀</div>
                <div>
                  <Translate id="ai_mindmap.aiflow_integration.gateway.point1">
                    One-click save to FunBlocks AIFlow workspace
                  </Translate>
                </div>
              </li>
              <li>
                <div className={mindmapStyles.featureIcon}>🔗</div>
                <div>
                  <Translate id="ai_mindmap.aiflow_integration.gateway.point2">
                    Seamless transition from browser to full AIFlow experience
                  </Translate>
                </div>
              </li>
              <li>
                <div className={mindmapStyles.featureIcon}>📈</div>
                <div>
                  <Translate id="ai_mindmap.aiflow_integration.gateway.point3">
                    Continue exploration with advanced thinking tools
                  </Translate>
                </div>
              </li>
            </ul>
          </div>
          <div className={mindmapStyles.featureImageWrapper}>
            <div className={mindmapStyles.featureImageContainer} onClick={() => setShowImageSrc("/img/portfolio/fullsize/aiflow_benefits.png")}>
              <img
                className={mindmapStyles.featureImage}
                id="ai-mindmap-integration"
                alt="AI Mindmap integration with FunBlocks AIFlow"
                src="/img/portfolio/thumbnails/aiflow_benefits.png"
              />

            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

export default function AIMindmap() {
  const [showImageSrc, setShowImageSrc] = useState(null);

  function getDomain() {
    if (!window.location.hostname.includes('funblocks')) {
      return 'funblocks.net';
    }
    return window.location.hostname.replace('www.', '');
  }

  function openUrl(url) {
    let newTab = window.open();
    newTab.location.href = url;
  }

  function toApp() {
    let url = `https://app.${getDomain()}/#/login?source=aiflow`;
    openUrl(url);
  }

  const testimonials_avatars = ["👨‍🎓", "👩‍💼", "👨‍🏫", "👩‍🎓", "👨‍💼", "👩‍🏫"];

  return (
    <Layout
      title={translate({
        id: 'ai_mindmap.head.title',
        message: 'FunBlocks AI Mindmap Extension - Transform Web Content into Mind Maps'
      })}
      description={translate({
        id: 'ai_mindmap.head.description',
        message: 'Transform any web page or YouTube video into visual mind maps with AI. One-click brainstorming, critical thinking, and seamless integration with FunBlocks AIFlow. Free Chrome extension.'
      })}
      keywords={translate({
        id: 'ai_mindmap.head.keywords',
        message: 'AI mindmap, mind mapping, Chrome extension, web content analysis, YouTube transcript, brainstorming, critical thinking, AIFlow, visual learning, knowledge mapping'
      })}
    >
      <MindmapStructuredData />
      <MindmapHeader setShowImageSrc={setShowImageSrc} />
      <main>
        <BenefitsSection
          page="ai_mindmap"
          backgroundColor="honeydew"
          customBenefits={[
            {
              icon: '⭐',
              titleId: 'ai_mindmap.benefits.benefit1.title',
              title: 'Visual Learning Enhancement',
              descriptionId: 'ai_mindmap.benefits.benefit1.description',
              description: 'Transform complex information into visual mind maps that improve comprehension and memory retention.'
            },
            {
              icon: '⚡',
              titleId: 'ai_mindmap.benefits.benefit2.title',
              title: 'Instant Knowledge Extraction',
              descriptionId: 'ai_mindmap.benefits.benefit2.description',
              description: 'Extract key insights from web pages and videos in seconds, saving hours of manual note-taking.'
            },
            {
              icon: '🎯',
              titleId: 'ai_mindmap.benefits.benefit3.title',
              title: 'Enhanced Focus & Clarity',
              descriptionId: 'ai_mindmap.benefits.benefit3.description',
              description: 'Organize scattered information into clear, structured mind maps that highlight important connections.'
            },
            {
              icon: '🔄',
              titleId: 'ai_mindmap.benefits.benefit4.title',
              title: 'Seamless Workflow Integration',
              descriptionId: 'ai_mindmap.benefits.benefit4.description',
              description: 'Start with any web content and seamlessly transition to deep exploration in FunBlocks AIFlow.'
            },
            {
              icon: '💡',
              titleId: 'ai_mindmap.benefits.benefit5.title',
              title: 'AI-Powered Insights',
              descriptionId: 'ai_mindmap.benefits.benefit5.description',
              description: 'Leverage AI to discover hidden patterns and generate new ideas from existing content.'
            },
            {
              icon: '📱',
              titleId: 'ai_mindmap.benefits.benefit6.title',
              title: 'Always Available',
              descriptionId: 'ai_mindmap.benefits.benefit6.description',
              description: 'Access powerful mind mapping capabilities wherever you browse, whenever inspiration strikes.'
            }
          ]}
        />
        {/* <MindmapShowcase /> */}

        <HowItWorksSection setShowImageSrc={setShowImageSrc} />
        <AIFlowIntegrationSection setShowImageSrc={setShowImageSrc} />
        <SocialProofSection page={'ai-mindmap'} showProductHuntBadges={true} />
        <TestimonialsSection avatars={testimonials_avatars} page={'ai_mindmap'} />
        <CTASection toApp={toApp} page={'ai_mindmap'} />
        <FAQSection
          page={'ai_mindmap'}
          faqIds={[
            'q1', 'q2', 'q3', 'q4', 'q5', 'q6', 'q7', 'q8'
          ]}
        />
      </main>
      <Footer />

      {/* Image Modal */}
      {showImageSrc && <ImageModal imageSrc={showImageSrc} setImageSrc={setShowImageSrc} />}
      <GoogleAccountAnalytics page={'ai-mindmap'} />
    </Layout>
  );
}
