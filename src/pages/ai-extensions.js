import React, { useState } from 'react';
import clsx from 'clsx';
import Link from '@docusaurus/Link';
import Layout from '@theme/Layout';
import Translate, { translate } from '@docusaurus/Translate';
import Heading from '@theme/Heading';

import styles from './index.module.css';
import extensionsStyles from './ai-extensions.module.css';
import Footer from '../components/Footer';
import FAQSection from '../components/FAQSection';
import TestimonialsSection from '../components/TestimonialsSection';
import ImageModal from '../components/ImageModal';
import GoogleAccountAnalytics from '../components/GoogleAccountAnalytics';
import CTASection from '../components/CTASection';
import ExtensionStructuredData from '../components/ExtensionStructuredData';
import ComparisonSection from '../components/ComparisonSection';
import VideoSection from '../components/VideoSection';
import BenefitsSection from '../components/BenefitsSection';
import SocialProofSection from '../components/SocialProofSection';

function ExtensionsHeader({ setShowImageSrc }) {
  return (
    <section id="hero" className={clsx(extensionsStyles.hero, extensionsStyles.pageSection)} style={{
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    }}>
      <div className="container">
        <div className={extensionsStyles.heroRow}>
          <div className={extensionsStyles.heroContent} style={{ flex: 1, minWidth: 0 }}>
            <div className={extensionsStyles.heroBadge}>
              <Translate id="ai_extensions.hero.badge">
                BROWSER EXTENSIONS
              </Translate>
            </div>
            <Heading as="h1">
              <Translate id="ai_extensions.hero.title">
                FunBlocks AI Browser Extensions
              </Translate>
            </Heading>
            <p className={extensionsStyles.heroSubtitle}>
              <Translate id="ai_extensions.hero.subtitle">
                Enhance your browsing experience with AI-powered reading, writing, and thinking tools. Available anytime, anywhere on the web.
              </Translate>
            </p>

            <div className={extensionsStyles.heroButtons}>
              <Link
                className={clsx('button', extensionsStyles.btnPrimary)}
                to="/welcome_extension"
              >
                <Translate id="ai_extensions.hero.explore">Explore Extensions</Translate>
              </Link>
              <Link
                className={clsx('button', extensionsStyles.btnSecondary)}
                to="#extensions-overview"
              >
                <Translate id="ai_extensions.hero.learn_more">Learn More</Translate>
              </Link>
            </div>

            <div className={extensionsStyles.heroStats}>
              <div className={extensionsStyles.heroStat}>
                <span className={extensionsStyles.heroStatNumber}>3</span>
                <span className={extensionsStyles.heroStatLabel}>
                  <Translate id="ai_extensions.hero.stat1">Powerful Extensions</Translate>
                </span>
              </div>
              <div className={extensionsStyles.heroStat}>
                <span className={extensionsStyles.heroStatNumber}>100K+</span>
                <span className={extensionsStyles.heroStatLabel}>
                  <Translate id="ai_extensions.hero.stat2">Active Users</Translate>
                </span>
              </div>
              <div className={extensionsStyles.heroStat}>
                <span className={extensionsStyles.heroStatNumber}>4.8★</span>
                <span className={extensionsStyles.heroStatLabel}>
                  <Translate id="ai_extensions.hero.stat3">User Rating</Translate>
                </span>
              </div>
            </div>
          </div>
          <div className={extensionsStyles.heroImageContainer} style={{ flex: 1, minWidth: 0, display: 'flex', justifyContent: 'center' }}>
            <div className={extensionsStyles.heroImageWrapper}>
              <img
                className={extensionsStyles.heroImage}
                onClick={() => setShowImageSrc("/img/portfolio/fullsize/all_in_one_en.png")}
                id="ai-extensions-overview"
                alt="FunBlocks AI Browser Extensions overview"
                src="/img/portfolio/fullsize/all_in_one_en.png"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

function ExtensionsBenefits() {
  const benefits = [
    {
      icon: '⚡',
      title: translate({
        id: 'ai_extensions.benefits.convenience.title',
        message: 'Always Available Convenience'
      }),
      description: translate({
        id: 'ai_extensions.benefits.convenience.description',
        message: 'Access powerful AI tools instantly while browsing any website or working on any task, without switching between applications.'
      })
    },
    {
      icon: '🔄',
      title: translate({
        id: 'ai_extensions.benefits.context.title',
        message: 'Automatic Context Capture'
      }),
      description: translate({
        id: 'ai_extensions.benefits.context.description',
        message: 'Automatically capture webpage content as context for AI assistance, eliminating the need for manual copy-pasting.'
      })
    },
    {
      icon: '🚀',
      title: translate({
        id: 'ai_extensions.benefits.efficiency.title',
        message: 'Dramatically Improved Efficiency'
      }),
      description: translate({
        id: 'ai_extensions.benefits.efficiency.description',
        message: 'Process web content with AI assistance to boost productivity by 10x in reading, writing, and thinking tasks.'
      })
    },
    {
      icon: '🧠',
      title: translate({
        id: 'ai_extensions.benefits.thinking.title',
        message: 'Enhanced Critical Thinking'
      }),
      description: translate({
        id: 'ai_extensions.benefits.thinking.description',
        message: 'Develop stronger analytical skills with AI-powered critical thinking frameworks and structured analysis tools.'
      })
    }
  ];

  return (
    <section className={extensionsStyles.benefitsSection}>
      <div className="container">
        <div className={extensionsStyles.sectionHeading}>
          <Heading as="h2" className={extensionsStyles.sectionTitle}>
            <Translate id="ai_extensions.benefits.title">Key Benefits</Translate>
          </Heading>
          <p className={extensionsStyles.sectionDescription}>
            <Translate id="ai_extensions.benefits.description">
              Discover how FunBlocks AI Browser Extensions transform your daily web experience with powerful AI capabilities.
            </Translate>
          </p>
        </div>

        <div className={extensionsStyles.benefitsGrid}>
          {benefits.map((benefit, index) => (
            <div key={index} className={extensionsStyles.benefitCard}>
              <div className={extensionsStyles.benefitIcon}>{benefit.icon}</div>
              <Heading as="h3" className={extensionsStyles.benefitTitle}>
                {benefit.title}
              </Heading>
              <p className={extensionsStyles.benefitDescription}>
                {benefit.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

function ExtensionsOverview({ setShowImageSrc }) {
  const extensions = [
    {
      id: 'funblocks-ai-extension',
      badge: 'COMPREHENSIVE',
      title: translate({
        id: 'ai_extensions.overview.extension1.title',
        message: 'FunBlocks AI Extension'
      }),
      subtitle: translate({
        id: 'ai_extensions.overview.extension1.subtitle',
        message: 'Complete AI-powered browsing assistant with writing, reading, and thinking tools'
      }),
      description: translate({
        id: 'ai_extensions.overview.extension1.description',
        message: 'The most comprehensive AI browser extension that includes all features from our other extensions plus advanced writing assistance, contextual AI tools, and seamless integration with FunBlocks AIFlow.'
      }),
      features: [
        translate({
          id: 'ai_extensions.overview.extension1.feature1',
          message: 'AI Writing Assistant with contextual toolbar'
        }),
        translate({
          id: 'ai_extensions.overview.extension1.feature2',
          message: 'AI Reading Assistant with critical thinking frameworks'
        }),
        translate({
          id: 'ai_extensions.overview.extension1.feature3',
          message: 'Mind mapping and brainstorming tools'
        }),
        translate({
          id: 'ai_extensions.overview.extension1.feature4',
          message: 'Smart widgets for email, video, and more'
        })
      ],
      link: '/welcome_extension',
      image: '/img/portfolio/fullsize/ai_reading_en.png',
      badgeColor: '#4CAF50'
    },
    {
      id: 'ai-mindmap-generator',
      badge: 'FOCUSED',
      title: translate({
        id: 'ai_extensions.overview.extension2.title',
        message: 'AI MindMap Generator'
      }),
      subtitle: translate({
        id: 'ai_extensions.overview.extension2.subtitle',
        message: 'Transform web content and videos into visual mind maps instantly'
      }),
      description: translate({
        id: 'ai_extensions.overview.extension2.description',
        message: 'Specialized extension focused on helping users read and understand web content and YouTube videos by generating visual mind maps, brainstorming, and critical analysis.'
      }),
      features: [
        translate({
          id: 'ai_extensions.overview.extension2.feature1',
          message: 'One-click web page mind mapping'
        }),
        translate({
          id: 'ai_extensions.overview.extension2.feature2',
          message: 'YouTube video transcript analysis'
        }),
        translate({
          id: 'ai_extensions.overview.extension2.feature3',
          message: 'AI-powered brainstorming and analysis'
        }),
        translate({
          id: 'ai_extensions.overview.extension2.feature4',
          message: 'Direct integration with FunBlocks AIFlow'
        })
      ],
      link: '/ai-mindmap',
      image: '/img/portfolio/fullsize/ai_mindmap_sidebar.png',
      badgeColor: '#2196F3'
    },
    {
      id: 'ai-prompt-optimizer',
      badge: 'SPECIALIZED',
      title: translate({
        id: 'ai_extensions.overview.extension3.title',
        message: 'AI Prompt Optimizer'
      }),
      subtitle: translate({
        id: 'ai_extensions.overview.extension3.subtitle',
        message: 'Enhance AI conversations with prompt optimization and critical thinking'
      }),
      description: translate({
        id: 'ai_extensions.overview.extension3.description',
        message: 'Specialized tool for improving AI interactions across ChatGPT, Claude, Gemini, and other AI platforms with prompt optimization, critical analysis, and thinking enhancement features.'
      }),
      features: [
        translate({
          id: 'ai_extensions.overview.extension3.feature1',
          message: 'One-click prompt optimization'
        }),
        translate({
          id: 'ai_extensions.overview.extension3.feature2',
          message: 'Critical thinking assistant'
        }),
        translate({
          id: 'ai_extensions.overview.extension3.feature3',
          message: 'Related questions and topics generation'
        }),
        translate({
          id: 'ai_extensions.overview.extension3.feature4',
          message: 'Multi-platform AI support'
        })
      ],
      link: '/prompt-optimizer',
      image: '/img/portfolio/fullsize/prompt_optimizer_hero.png',
      badgeColor: '#FF9800'
    }
  ];

  return (
    <section id="extensions-overview" className={extensionsStyles.overviewSection}>
      <div className="container">
        <div className={extensionsStyles.sectionHeading}>
          <Heading as="h2" className={extensionsStyles.sectionTitle}>
            <Translate id="ai_extensions.overview.title">Our Browser Extensions</Translate>
          </Heading>
          <p className={extensionsStyles.sectionDescription}>
            <Translate id="ai_extensions.overview.description">
              Choose the perfect AI extension for your needs. Each extension is designed for specific use cases while maintaining seamless integration with the FunBlocks AI ecosystem.
            </Translate>
          </p>
        </div>

        <div className={extensionsStyles.extensionsGrid}>
          {extensions.map((extension, index) => (
            <div key={extension.id} className={extensionsStyles.extensionCard}>
              <div className={extensionsStyles.extensionImageWrapper}>
                <img
                  className={extensionsStyles.extensionImage}
                  onClick={() => setShowImageSrc(extension.image)}
                  alt={extension.title}
                  src={extension.image}
                />
                <div
                  className={extensionsStyles.extensionBadge}
                  style={{ backgroundColor: extension.badgeColor }}
                >
                  {extension.badge}
                </div>
              </div>

              <div className={extensionsStyles.extensionContent}>
                <Heading as="h3" className={extensionsStyles.extensionTitle}>
                  {extension.title}
                </Heading>
                <p className={extensionsStyles.extensionSubtitle}>
                  {extension.subtitle}
                </p>
                <p className={extensionsStyles.extensionDescription}>
                  {extension.description}
                </p>

                <ul className={extensionsStyles.extensionFeatures}>
                  {extension.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className={extensionsStyles.extensionFeature}>
                      <span className={extensionsStyles.featureIcon}>✓</span>
                      {feature}
                    </li>
                  ))}
                </ul>

                <Link
                  className={clsx('button', extensionsStyles.extensionButton)}
                  to={extension.link}
                >
                  <Translate id="ai_extensions.overview.learn_more">Learn More</Translate>
                </Link>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

function ExtensionComparison() {
  return (
    <section className={extensionsStyles.comparisonSection}>
      <div className="container">
        <div className={extensionsStyles.sectionHeading}>
          <Heading as="h2" className={extensionsStyles.sectionTitle}>
            <Translate id="ai_extensions.comparison.title">Extension Comparison</Translate>
          </Heading>
          <p className={extensionsStyles.sectionDescription}>
            <Translate id="ai_extensions.comparison.description">
              Understand the differences between our extensions to choose the right one for your needs.
            </Translate>
          </p>
        </div>

        <div className={extensionsStyles.comparisonTable}>
          <div className={extensionsStyles.comparisonHeader}>
            <div className={extensionsStyles.comparisonFeature}>
              <Translate id="ai_extensions.comparison.feature">Feature</Translate>
            </div>
            <div className={extensionsStyles.comparisonExtension}>
              <Translate id="ai_extensions.comparison.funblocks_ai">FunBlocks AI Extension</Translate>
            </div>
            <div className={extensionsStyles.comparisonExtension}>
              <Translate id="ai_extensions.comparison.mindmap">AI MindMap Generator</Translate>
            </div>
            <div className={extensionsStyles.comparisonExtension}>
              <Translate id="ai_extensions.comparison.prompt_optimizer">AI Prompt Optimizer</Translate>
            </div>
          </div>

          <div className={extensionsStyles.comparisonRow}>
            <div className={extensionsStyles.comparisonFeature}>
              <Translate id="ai_extensions.comparison.writing_assistant">AI Writing Assistant</Translate>
            </div>
            <div className={extensionsStyles.comparisonCell}>✅</div>
            <div className={extensionsStyles.comparisonCell}>❌</div>
            <div className={extensionsStyles.comparisonCell}>❌</div>
          </div>

          <div className={extensionsStyles.comparisonRow}>
            <div className={extensionsStyles.comparisonFeature}>
              <Translate id="ai_extensions.comparison.mindmapping">Mind Mapping</Translate>
            </div>
            <div className={extensionsStyles.comparisonCell}>✅</div>
            <div className={extensionsStyles.comparisonCell}>✅</div>
            <div className={extensionsStyles.comparisonCell}>❌</div>
          </div>

          <div className={extensionsStyles.comparisonRow}>
            <div className={extensionsStyles.comparisonFeature}>
              <Translate id="ai_extensions.comparison.prompt_optimization">Prompt Optimization</Translate>
            </div>
            <div className={extensionsStyles.comparisonCell}>❌</div>
            <div className={extensionsStyles.comparisonCell}>❌</div>
            <div className={extensionsStyles.comparisonCell}>✅</div>
          </div>

          <div className={extensionsStyles.comparisonRow}>
            <div className={extensionsStyles.comparisonFeature}>
              <Translate id="ai_extensions.comparison.critical_thinking">Critical Thinking Tools</Translate>
            </div>
            <div className={extensionsStyles.comparisonCell}>✅</div>
            <div className={extensionsStyles.comparisonCell}>✅</div>
            <div className={extensionsStyles.comparisonCell}>✅</div>
          </div>

          <div className={extensionsStyles.comparisonRow}>
            <div className={extensionsStyles.comparisonFeature}>
              <Translate id="ai_extensions.comparison.contextual_tools">Contextual AI Tools</Translate>
            </div>
            <div className={extensionsStyles.comparisonCell}>✅</div>
            <div className={extensionsStyles.comparisonCell}>Limited</div>
            <div className={extensionsStyles.comparisonCell}>❌</div>
          </div>

          <div className={extensionsStyles.comparisonRow}>
            <div className={extensionsStyles.comparisonFeature}>
              <Translate id="ai_extensions.comparison.aiflow_integration">AIFlow Integration</Translate>
            </div>
            <div className={extensionsStyles.comparisonCell}>✅</div>
            <div className={extensionsStyles.comparisonCell}>✅</div>
            <div className={extensionsStyles.comparisonCell}>❌</div>
          </div>
        </div>

        <div className={extensionsStyles.comparisonNote}>
          <p>
            <Translate id="ai_extensions.comparison.note">
              The FunBlocks AI Extension includes all features from the AI MindMap Generator plus additional writing and contextual tools. Choose the specialized extensions if you only need specific functionality.
            </Translate>
          </p>
        </div>
      </div>
    </section>
  );
}

export default function AIExtensions() {
  const [showImageSrc, setShowImageSrc] = useState(null);

  const faqData = [
    {
      question: translate({
        id: 'ai_extensions.faq.q1',
        message: 'What is the difference between FunBlocks AI Extension and AI MindMap Generator?'
      }),
      answer: translate({
        id: 'ai_extensions.faq.a1',
        message: 'The FunBlocks AI Extension is comprehensive and includes all features from the AI MindMap Generator plus additional writing assistance, contextual AI tools, and smart widgets. The AI MindMap Generator is specialized for reading web content and videos, generating mind maps and brainstorming.'
      })
    },
    {
      question: translate({
        id: 'ai_extensions.faq.q2',
        message: 'Which extension should I choose?'
      }),
      answer: translate({
        id: 'ai_extensions.faq.a2',
        message: 'Choose FunBlocks AI Extension for comprehensive AI assistance across all web activities. Choose AI MindMap Generator if you primarily need mind mapping and content analysis. Choose AI Prompt Optimizer if you want to enhance your interactions with AI platforms like ChatGPT and Claude.'
      })
    },
    {
      question: translate({
        id: 'ai_extensions.faq.q3',
        message: 'Are these extensions free to use?'
      }),
      answer: translate({
        id: 'ai_extensions.faq.a3',
        message: 'Yes, all extensions offer free usage with daily quotas. New users receive bonus credits, and you can upgrade to FunBlocks AI plans for unlimited usage and additional features.'
      })
    },
    {
      question: translate({
        id: 'ai_extensions.faq.q4',
        message: 'Do I need to install all three extensions?'
      }),
      answer: translate({
        id: 'ai_extensions.faq.a4',
        message: 'No, you can choose based on your needs. The FunBlocks AI Extension provides the most comprehensive experience, while the other two are specialized for specific use cases.'
      })
    }
  ];

  return (
    <Layout
      title={translate({
        id: 'ai_extensions.meta.title',
        message: 'FunBlocks AI Browser Extensions - AI-Powered Web Tools'
      })}
      description={translate({
        id: 'ai_extensions.meta.description',
        message: 'Enhance your browsing with FunBlocks AI Extensions: AI Assistant, Mindmap Generator, and Prompt Optimizer. Boost productivity with AI-powered reading, writing, and thinking tools.'
      })}
    >
      <ExtensionStructuredData />
      <GoogleAccountAnalytics />

      <ExtensionsHeader setShowImageSrc={setShowImageSrc} />
      <ExtensionsBenefits />
      <ExtensionsOverview setShowImageSrc={setShowImageSrc} />
      <ExtensionComparison />

      <SocialProofSection />

      <FAQSection
        title={translate({
          id: 'ai_extensions.faq.title',
          message: 'Frequently Asked Questions'
        })}
        faqData={faqData}
      />

      <CTASection
        title={translate({
          id: 'ai_extensions.cta.title',
          message: 'Ready to Transform Your Browsing Experience?'
        })}
        description={translate({
          id: 'ai_extensions.cta.description',
          message: 'Join thousands of users who have enhanced their productivity with FunBlocks AI Browser Extensions.'
        })}
        primaryButton={{
          text: translate({
            id: 'ai_extensions.cta.primary',
            message: 'Get Started Free'
          }),
          link: '/welcome_extension'
        }}
        secondaryButton={{
          text: translate({
            id: 'ai_extensions.cta.secondary',
            message: 'View All Extensions'
          }),
          link: '#extensions-overview'
        }}
      />

      <Footer />

      {showImageSrc && (
        <ImageModal
          src={showImageSrc}
          onClose={() => setShowImageSrc(null)}
        />
      )}
    </Layout>
  );
}
